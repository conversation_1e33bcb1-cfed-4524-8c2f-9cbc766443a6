from flask import Flask, request, render_template, redirect ,session
import mysql.connector
from groq import Groq

app = Flask(__name__)
app.secret_key = 'your-secret-key12345'

import uuid

# Initialize Groq client for sentiment analysis
client = Groq(api_key="********************************************************")

# Enhanced role & context + few-shot examples (sentiment-specific)
training_examples = """
You are a sentiment analysis agent with deep expertise in workplace psychology and human resources.

Role: Expert Sentiment Analyst AI
Domain: Analyze employee feedback related to categories like Diversity, Equity, Inclusion, Leadership, Policy, Workplace Culture, and Employee Engagement.
Task: Given a sentence related to workplace experience, analyze and classify its sentiment.
Output Format: Respond ONLY with one of the following words — Positive, Negative, or Neutral.
Thinking Process: Think carefully about the tone, intent, and emotional polarity of the sentence. Consider if the sentence expresses satisfaction, dissatisfaction, or neutrality. Do not assume based on topic, only sentiment. Avoid explanation — just the final sentiment output is needed.

Example 1:
Sentence: I absolutely love the work environment here.
Sentiment: Positive

Example 2:
Sentence: This place is terrible to work at.
Sentiment: Negative

Example 3:
Sentence: You have career development opportunities to learn and grow in your current role, Sometimes.
Sentiment: Neutral

Example 4:
Sentence: The management is helpful and encouraging.
Sentiment: Positive

Example 5:
Sentence: "You feel that your workplace or organisation's policies support you no matter your background of experience and knowledge. Needs for improvement."
Sentiment: Neutral

Example 6:
Sentence: The workload is overwhelming and unfair.
Sentiment: Negative
"""



def predict_sentiment(user_sentence):
    """Predict sentiment of user input using AI"""
    try:
        full_prompt = f"""{training_examples}

Sentence: {user_sentence}
Sentiment:"""

        response = client.chat.completions.create(
            model="llama3-70b-8192",
            messages=[
                {"role": "user", "content": full_prompt}
            ],
            temperature=0.2,
        )

        return response.choices[0].message.content.strip()
    except Exception as e:
        print(f"Error in sentiment prediction: {e}")
        return "Neutral"  # Default fallback


# MySQL connection
db = mysql.connector.connect(
    host="fz-website-registration.c9uy0agkwmgg.ap-south-1.rds.amazonaws.com",
    user="admin",
    password="master123",
    database="registration"
)
cursor = db.cursor()

@app.route('/add_question/<email>', methods=['POST'])
def add_question(email):
    question = request.form['question']
    option_1 = request.form['option_1']
    option_2 = request.form['option_2']
    option_3 = request.form['option_3']
    option_4 = request.form['option_4']

    # Get the next available question number
    cursor.execute("SELECT MAX(question_number) FROM employee_questions WHERE email = %s", (email,))
    result = cursor.fetchone()
    next_question_number = (result[0] or 0) + 1

    cursor.execute("""
        INSERT INTO employee_questions (
            email, question_number, question_text, option_1, option_2, option_3, option_4
        ) VALUES (%s, %s, %s, %s, %s, %s, %s)
    """, (email, next_question_number, question, option_1, option_2, option_3, option_4))

    db.commit()
    return redirect(f'/view_questions/{email}')

@app.route('/edit_question/<email>/<int:question_number>', methods=['POST'])
def edit_question(email, question_number):
    new_text = request.form['new_text']

    cursor.execute("""
        UPDATE employee_questions
        SET question_text = %s
        WHERE email = %s AND question_number = %s
    """, (new_text, email, question_number))
    db.commit()

    return redirect(f'/view_questions/{email}')


@app.route('/test/<email>', methods=['GET', 'POST'])
def test(email):
    if request.method == 'POST':
        if 'gender' in request.form:
            # Save form data in session
            session['email'] = email
            session['gender'] = request.form['gender']
            session['age_group'] = request.form['age_group']
            session['tenure_group'] = request.form['tenure_group']
            session['role'] = request.form['role']
            session['department'] = request.form['department']
            session['unique_id'] = str(uuid.uuid4())

            return redirect(f'/test/{email}?step=questions')

        else:
            # Final submission – store questions + session data in student_data with AI analysis
            for key in request.form:
                if key.startswith('question_') and not key.startswith('question_text_'):
                    question_number = int(key.split('_')[1])
                    selected_option = request.form[key]
                    question_text = request.form[f'question_text_{question_number}']

                    # Combine question and answer for better sentiment analysis
                    combined_text = f"{question_text} {selected_option}"

                    # Perform AI sentiment analysis
                    print(f"Analyzing sentiment for: {combined_text}")
                    predicted_sentiment = predict_sentiment(combined_text)

                    print(f"Predicted Sentiment: {predicted_sentiment}")

                    cursor.execute("""
                        INSERT INTO student_data (
                            question_number, question_text, selected_text, form_url,
                            unique_id, gender, age_group, tenure_group, role, department,
                            predicted_sentiment
                        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                    """, (
                        question_number, question_text, selected_option, f"/test/{email}",
                        session['unique_id'], session['gender'], session['age_group'],
                        session['tenure_group'], session['role'], session['department'],
                        predicted_sentiment
                    ))

            db.commit()
            session.clear()
            return "Thank you! Test submitted successfully. Your responses have been analyzed for sentiment."

    # GET Requests
    if request.args.get('step') == 'questions':
        # Show the survey questions
        cursor.execute("""
            SELECT question_number, question_text, option_1, option_2, option_3, option_4
            FROM employee_questions
            WHERE email = %s
            ORDER BY question_number
        """, (email,))
        rows = cursor.fetchall()

        questions = [
            {
                'question_number': row[0],
                'question_text': row[1],
                'option_1': row[2],
                'option_2': row[3],
                'option_3': row[4],
                'option_4': row[5]
            }
            for row in rows
        ]
        return render_template('test.html', questions=questions)

    # Step 1: Show user detail form
    return render_template('test_user_info.html', email=email)

@app.route('/delete_question/<email>/<int:question_number>', methods=['POST'])
def delete_question(email, question_number):
    cursor.execute("""
        DELETE FROM employee_questions
        WHERE email = %s AND question_number = %s
    """, (email, question_number))
    db.commit()

    # Renumber remaining questions
    cursor.execute("""
        SELECT question_number FROM employee_questions
        WHERE email = %s ORDER BY question_number
    """, (email,))
    all_questions = cursor.fetchall()

    for new_number, row in enumerate(all_questions, start=1):
        old_number = row[0]
        cursor.execute("""
            UPDATE employee_questions
            SET question_number = %s
            WHERE email = %s AND question_number = %s
        """, (new_number, email, old_number))

    db.commit()
    return redirect(f"/view_questions/{email}")

@app.route('/view_questions/<email>', methods=['GET'])
def view_questions(email):
    cursor.execute("""
        SELECT question_number, question_text
        FROM employee_questions
        WHERE email = %s
        ORDER BY question_number
    """, (email,))
    rows = cursor.fetchall()

    questions = [{'question_number': row[0], 'question_text': row[1]} for row in rows]

    return render_template('view_questions.html', questions=questions, email=email)


if __name__ == '__main__':
    app.run(debug=True)

