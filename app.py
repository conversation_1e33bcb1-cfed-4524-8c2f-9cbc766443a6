from flask import Flask, request, render_template, redirect ,session
import mysql.connector

app = Flask(__name__)
app.secret_key = 'your-secret-key12345'

import uuid
# MySQL connection
db = mysql.connector.connect(
    host="fz-website-registration.c9uy0agkwmgg.ap-south-1.rds.amazonaws.com",
    user="admin",
    password="master123",
    database="registration"
)
cursor = db.cursor()

@app.route('/add_question/<email>', methods=['POST'])
def add_question(email):
    question = request.form['question']
    option_1 = request.form['option_1']
    option_2 = request.form['option_2']
    option_3 = request.form['option_3']
    option_4 = request.form['option_4']

    # Get the next available question number
    cursor.execute("SELECT MAX(question_number) FROM employee_questions WHERE email = %s", (email,))
    result = cursor.fetchone()
    next_question_number = (result[0] or 0) + 1

    cursor.execute("""
        INSERT INTO employee_questions (
            email, question_number, question_text, option_1, option_2, option_3, option_4
        ) VALUES (%s, %s, %s, %s, %s, %s, %s)
    """, (email, next_question_number, question, option_1, option_2, option_3, option_4))

    db.commit()
    return redirect(f'/view_questions/{email}')

@app.route('/edit_question/<email>/<int:question_number>', methods=['POST'])
def edit_question(email, question_number):
    new_text = request.form['new_text']

    cursor.execute("""
        UPDATE employee_questions
        SET question_text = %s
        WHERE email = %s AND question_number = %s
    """, (new_text, email, question_number))
    db.commit()

    return redirect(f'/view_questions/{email}')


@app.route('/test/<email>', methods=['GET', 'POST'])
def test(email):
    if request.method == 'POST':
        if 'gender' in request.form:
            # Save form data in session
            session['email'] = email
            session['gender'] = request.form['gender']
            session['age_group'] = request.form['age_group']
            session['tenure_group'] = request.form['tenure_group']
            session['role'] = request.form['role']
            session['department'] = request.form['department']
            session['unique_id'] = str(uuid.uuid4())

            return redirect(f'/test/{email}?step=questions')

        else:
            # Final submission – store questions + session data in student_data
            for key in request.form:
                if key.startswith('question_') and not key.startswith('question_text_'):
                    question_number = int(key.split('_')[1])
                    selected_option = request.form[key]
                    question_text = request.form[f'question_text_{question_number}']

                    cursor.execute("""
                        INSERT INTO student_data (
                            question_number, question_text, selected_text, form_url,
                            unique_id, gender, age_group, tenure_group, role, department, predicted_sentiment
                        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                    """, (
                        question_number, question_text, selected_option, f"/test/{email}",
                        session['unique_id'], session['gender'], session['age_group'],
                        session['tenure_group'], session['role'], session['department'], ''
                    ))

            db.commit()
            session.clear()
            return "Thank you! Test submitted successfully."

    # GET Requests
    if request.args.get('step') == 'questions':
        # Show the survey questions
        cursor.execute("""
            SELECT question_number, question_text, option_1, option_2, option_3, option_4
            FROM employee_questions
            WHERE email = %s
            ORDER BY question_number
        """, (email,))
        rows = cursor.fetchall()

        questions = [
            {
                'question_number': row[0],
                'question_text': row[1],
                'option_1': row[2],
                'option_2': row[3],
                'option_3': row[4],
                'option_4': row[5]
            }
            for row in rows
        ]
        return render_template('test.html', questions=questions)

    # Step 1: Show user detail form
    return render_template('test_user_info.html', email=email)

@app.route('/delete_question/<email>/<int:question_number>', methods=['POST'])
def delete_question(email, question_number):
    cursor.execute("""
        DELETE FROM employee_questions
        WHERE email = %s AND question_number = %s
    """, (email, question_number))
    db.commit()

    # Renumber remaining questions
    cursor.execute("""
        SELECT question_number FROM employee_questions
        WHERE email = %s ORDER BY question_number
    """, (email,))
    all_questions = cursor.fetchall()

    for new_number, row in enumerate(all_questions, start=1):
        old_number = row[0]
        cursor.execute("""
            UPDATE employee_questions
            SET question_number = %s
            WHERE email = %s AND question_number = %s
        """, (new_number, email, old_number))

    db.commit()
    return redirect(f"/view_questions/{email}")

@app.route('/view_questions/<email>', methods=['GET'])
def view_questions(email):
    cursor.execute("""
        SELECT question_number, question_text
        FROM employee_questions
        WHERE email = %s
        ORDER BY question_number
    """, (email,))
    rows = cursor.fetchall()

    questions = [{'question_number': row[0], 'question_text': row[1]} for row in rows]

    return render_template('view_questions.html', questions=questions, email=email)


if __name__ == '__main__':
    app.run(debug=True)

