<!DOCTYPE html>
<html>
<head>
    <title>Manage Questions</title>
    <style>
        .inline-btn {
            display: inline;
            margin-left: 10px;
        }
    </style>
</head>
<body>
    <h2>Questions for {{ email }}</h2>

    {% for q in questions %}
    <div>
        <p>
            <strong>Q{{ q.question_number }}:</strong>

            {% if request.args.get('edit') == q.question_number|string %}
                <form action="/edit_question/{{ email }}/{{ q.question_number }}" method="POST" class="inline-btn">
                    <input type="text" name="new_text" value="{{ q.question_text }}" required>
                    <button type="submit">Save</button>
                </form>
            {% else %}
                {{ q.question_text }}
                <!-- Edit button with pencil icon -->
                <a href="/view_questions/{{ email }}?edit={{ q.question_number }}" class="inline-btn" title="Edit">
                    ✏️
                </a>
            {% endif %}
            
            <!-- Delete button -->
            <form action="/delete_question/{{ email }}/{{ q.question_number }}" method="POST" class="inline-btn">
                <input type="submit" value="Delete">
            </form>
        </p>
    </div>
    <hr>
    {% endfor %}

    <h3>Add a New Question</h3>
    <form action="/add_question/{{ email }}" method="POST">
        Question: <input type="text" name="question" required><br>
        Option 1: <input type="text" name="option_1" required><br>
        Option 2: <input type="text" name="option_2" required><br>
        Option 3: <input type="text" name="option_3" required><br>
        Option 4: <input type="text" name="option_4" required><br>
        <br>
        <input type="submit" value="Add Question">
    </form>
</body>
</html>
