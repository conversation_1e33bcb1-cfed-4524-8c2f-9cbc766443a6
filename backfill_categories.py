import mysql.connector
from groq import Groq

# Initialize Groq client
client = Groq(api_key="********************************************************")

# Category classification prompt template
category_instruction = """
You are an AI trained to identify one or more workplace categories that best match a given employee statement,sometimes the sentence can be indirectly related to some categories so understand the context and find the best match.

Role: Expert Workplace Feedback Classifier 
Categories: Diversity, Equity, Inclusion, Leadership, Policy, Workplace Culture, Employee Engagement, strategic_alignment, culture_engagement, support_motivation, skill_development

Task: Given a sentence or question, predict which category or categories it belongs to. Respond only with category names separated by commas. Do not explain.

Example 1:
Sentence: I love how inclusive our company is toward people of different backgrounds.
Category: Inclusion, Diversity

Example 2:
Sentence: My manager is very supportive and always motivates us.
Category: Leadership, Employee Engagement

Example 3:
Sentence: The new remote work policy is confusing and not employee-friendly.
Category: Policy, Workplace Culture

Example 4:
Sentence: Everyone gets equal chances at promotion here.
Category: Equity

Example 5:
Sentence: I don't feel connected to the company's mission lately.
Category: Employee Engagement

Sentence: {sentence}
Category:
"""

def predict_category(user_sentence):
    """Predict category of user input using AI"""
    try:
        full_prompt = category_instruction.format(sentence=user_sentence)

        response = client.chat.completions.create(
            model="llama3-70b-8192",
            messages=[
                {"role": "user", "content": full_prompt}
            ],
            temperature=0.2,
        )

        return response.choices[0].message.content.strip()
    except Exception as e:
        print(f"Error in category prediction: {e}")
        return "Employee Engagement"  # Default fallback

# MySQL connection
db = mysql.connector.connect(
    host="fz-website-registration.c9uy0agkwmgg.ap-south-1.rds.amazonaws.com",
    user="admin",
    password="master123",
    database="registration"
)
cursor = db.cursor()

print("🔄 Backfilling Categories for Existing Records")
print("=" * 50)

try:
    # Get records that have sentiment but no category
    cursor.execute("""
        SELECT id, question_text, selected_text 
        FROM student_data 
        WHERE predicted_sentiment != '' AND (predicted_category = '' OR predicted_category IS NULL)
        LIMIT 20
    """)
    
    records = cursor.fetchall()
    
    if not records:
        print("✅ All records already have categories assigned!")
    else:
        print(f"📝 Processing {len(records)} records...")
        
        for record_id, question_text, selected_text in records:
            # Combine question and answer for better category prediction
            combined_text = f"{question_text} {selected_text}"
            
            # Predict category
            predicted_category = predict_category(combined_text)
            
            # Update the record
            cursor.execute("""
                UPDATE student_data 
                SET predicted_category = %s 
                WHERE id = %s
            """, (predicted_category, record_id))
            
            print(f"  ✓ Record {record_id}: {predicted_category}")
        
        db.commit()
        print(f"\n✅ Successfully updated {len(records)} records with category predictions!")

except mysql.connector.Error as err:
    print(f"❌ Database Error: {err}")
    
finally:
    cursor.close()
    db.close()

print("\n🎯 Backfill Complete!")
print("All existing records now have both sentiment and category analysis.")
