<!-- templates/test_user_info.html -->
<!DOCTYPE html>
<html>
<head>
    <title>Enter User Info</title>
</head>
<body>
    <h2>Enter Your Details</h2>
    <form method="POST">
        <label>Gender:</label>
        <select name="gender" required>
            <option value="">Select</option>
            <option>Male</option>
            <option>Female</option>
            <option>Other</option>
        </select><br>

        <label>Age Group:</label>
        <select name="age_group" required>
            <option value="">Select</option>
            <option>18-25</option>
            <option>26-35</option>
            <option>36-45</option>
            <option>46+</option>
        </select><br>

        <label>Tenure Group:</label>
        <select name="tenure_group" required>
            <option value="">Select</option>
            <option>30 days-1 year</option>
            <option>1-3 years</option>
            <option>3-5 years</option>
            <option>5+ years</option>
        </select><br>

        <label>Role:</label>
        <select name="role" required>
            <option value="">Select</option>
            <option>Junior Staff</option>
            <option>Senior Staff</option>
            <option>Manager</option>
            <option>Executive</option>
            <option>Trainee</option>
            <option>Team Member</option>
        </select><br>

        <label>Department:</label>
        <select name="department" required onchange="toggleOther(this.value)">
            <option value="">Select</option>
            <option>HR</option>
            <option>Finance</option>
            <option>Technical</option>
            <option>Operations</option>
            <option>Others</option>
        </select><br>

        <div id="otherDept" style="display:none;">
            <label>Other Department:</label>
            <input type="text" name="other_department">
        </div>

        <br><input type="submit" value="Next">
    </form>

    <script>
        function toggleOther(val) {
            document.getElementById('otherDept').style.display = val === 'Others' ? 'block' : 'none';
        }
    </script>
</body>
</html>
