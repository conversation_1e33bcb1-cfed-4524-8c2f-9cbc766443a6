import mysql.connector

# MySQL connection
db = mysql.connector.connect(
    host="fz-website-registration.c9uy0agkwmgg.ap-south-1.rds.amazonaws.com",
    user="admin",
    password="master123",
    database="registration"
)
cursor = db.cursor()

try:
    # Add predicted_category column to student_data table
    cursor.execute("""
        ALTER TABLE student_data 
        ADD COLUMN predicted_category VARCHAR(500) DEFAULT ''
    """)
    
    db.commit()
    print("✅ Successfully added 'predicted_category' column to student_data table")
    
except mysql.connector.Error as err:
    if err.errno == 1060:  # Duplicate column name
        print("⚠️  Column 'predicted_category' already exists in the table")
    else:
        print(f"❌ Error: {err}")
        
finally:
    cursor.close()
    db.close()
