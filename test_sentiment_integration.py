from groq import Groq

# Initialize Groq client for testing
client = Groq(api_key="********************************************************")

# Test the sentiment analysis functions (copied from app.py)
training_examples = """
You are a sentiment analysis agent with deep expertise in workplace psychology and human resources.

Role: Expert Sentiment Analyst AI
Domain: Analyze employee feedback related to categories like Diversity, Equity, Inclusion, Leadership, Policy, Workplace Culture, and Employee Engagement.
Task: Given a sentence related to workplace experience, analyze and classify its sentiment.
Output Format: Respond ONLY with one of the following words — Positive, Negative, or Neutral.
Thinking Process: Think carefully about the tone, intent, and emotional polarity of the sentence. Consider if the sentence expresses satisfaction, dissatisfaction, or neutrality. Do not assume based on topic, only sentiment. Avoid explanation — just the final sentiment output is needed.

Example 1:
Sentence: I absolutely love the work environment here.
Sentiment: Positive

Example 2:
Sentence: This place is terrible to work at.
Sentiment: Negative

Example 3:
Sentence: You have career development opportunities to learn and grow in your current role, Sometimes.
Sentiment: Neutral

Example 4:
Sentence: The management is helpful and encouraging.
Sentiment: Positive

Example 5:
Sentence: "You feel that your workplace or organisation's policies support you no matter your background of experience and knowledge. Needs for improvement."
Sentiment: Neutral

Example 6:
Sentence: The workload is overwhelming and unfair.
Sentiment: Negative
"""

def predict_sentiment(user_sentence):
    """Predict sentiment of user input using AI"""
    try:
        full_prompt = f"""{training_examples}

Sentence: {user_sentence}
Sentiment:"""

        response = client.chat.completions.create(
            model="llama3-70b-8192",
            messages=[
                {"role": "user", "content": full_prompt}
            ],
            temperature=0.2,
        )

        return response.choices[0].message.content.strip()
    except Exception as e:
        print(f"Error in sentiment prediction: {e}")
        return "Neutral"

# Test cases
test_cases = [
    "How satisfied are you with your work environment? Very satisfied",
    "Do you feel supported by your manager? Not at all",
    "Rate your work-life balance Neutral",
    "How inclusive is your workplace? Extremely inclusive and welcoming",
    "Are you happy with your career growth? I'm frustrated with lack of opportunities"
]

print("🧪 Testing Sentiment Analysis Integration")
print("=" * 50)

for i, test_case in enumerate(test_cases, 1):
    print(f"\nTest {i}:")
    print(f"Input: {test_case}")
    sentiment = predict_sentiment(test_case)
    print(f"Predicted Sentiment: {sentiment}")
    print("-" * 30)

print("\n✅ Sentiment analysis integration test completed!")
